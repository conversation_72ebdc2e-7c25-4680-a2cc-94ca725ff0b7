"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var service_worker_exports = {};
__export(service_worker_exports, {
  fire: () => fire,
  handle: () => import_handler.handle
});
module.exports = __toCommonJS(service_worker_exports);
var import_handler = require("./handler");
const fire = (app, options = {
  fetch: void 0
}) => {
  addEventListener("fetch", (0, import_handler.handle)(app, options));
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  fire,
  handle
});
