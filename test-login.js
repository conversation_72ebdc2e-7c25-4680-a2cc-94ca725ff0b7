// test-login.js - 简单的登录测试服务器
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { serve } from '@hono/node-server';

const app = new Hono();

// 使用 CORS 中间件
app.use(cors());

// 管理员登录信息
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123root';

// 基础健康检查接口
app.get('/', (c) => {
  console.log('Root endpoint accessed');
  return c.text('Test Backend is running!');
});

// 登录接口
app.post('/api/login', async (c) => {
  try {
    console.log('Login request received');
    const body = await c.req.json();
    console.log('Request body:', body);
    
    const { username, password } = body;
    console.log('Username:', username, 'Password:', password);
    console.log('Expected username:', ADMIN_USERNAME, 'Expected password:', ADMIN_PASSWORD);

    if (username === ADMIN_USERNAME && password === ADMIN_PASSWORD) {
      console.log('Login successful');
      return c.json({ success: true, message: '登录成功，欢迎管理员！' });
    } else {
      console.log('Login failed - invalid credentials');
      return c.json({ success: false, message: '用户名或密码错误' }, 401);
    }
  } catch (error) {
    console.error('Login error:', error);
    return c.json({ success: false, message: '服务器错误: ' + error.message }, 500);
  }
});

// 启动服务器
const port = 3001; // 使用不同的端口避免冲突
console.log(`Starting test server on port ${port}`);

serve({
  fetch: app.fetch,
  port: port,
});

console.log(`Test server is running on http://localhost:${port}`);
