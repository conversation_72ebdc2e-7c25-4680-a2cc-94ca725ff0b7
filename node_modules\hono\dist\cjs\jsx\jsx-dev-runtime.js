"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var jsx_dev_runtime_exports = {};
__export(jsx_dev_runtime_exports, {
  Fragment: () => import_base2.Fragment,
  jsxDEV: () => jsxDEV
});
module.exports = __toCommonJS(jsx_dev_runtime_exports);
var import_base = require("./base");
var import_base2 = require("./base");
function jsxDEV(tag, props, key) {
  let node;
  if (!props || !("children" in props)) {
    node = (0, import_base.jsxFn)(tag, props, []);
  } else {
    const children = props.children;
    node = Array.isArray(children) ? (0, import_base.jsxFn)(tag, props, children) : (0, import_base.jsxFn)(tag, props, [children]);
  }
  node.key = key;
  return node;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Fragment,
  jsxDEV
});
