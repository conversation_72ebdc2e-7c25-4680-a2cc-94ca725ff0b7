// src/utils/crypto.ts
var sha256 = async (data) => {
  const algorithm = { name: "SHA-256", alias: "sha256" };
  const hash = await createHash(data, algorithm);
  return hash;
};
var sha1 = async (data) => {
  const algorithm = { name: "SHA-1", alias: "sha1" };
  const hash = await createHash(data, algorithm);
  return hash;
};
var md5 = async (data) => {
  const algorithm = { name: "MD5", alias: "md5" };
  const hash = await createHash(data, algorithm);
  return hash;
};
var createHash = async (data, algorithm) => {
  let sourceBuffer;
  if (ArrayBuffer.isView(data) || data instanceof ArrayBuffer) {
    sourceBuffer = data;
  } else {
    if (typeof data === "object") {
      data = JSON.stringify(data);
    }
    sourceBuffer = new TextEncoder().encode(String(data));
  }
  if (crypto && crypto.subtle) {
    const buffer = await crypto.subtle.digest(
      {
        name: algorithm.name
      },
      sourceBuffer
    );
    const hash = Array.prototype.map.call(new Uint8Array(buffer), (x) => ("00" + x.toString(16)).slice(-2)).join("");
    return hash;
  }
  return null;
};
export {
  createHash,
  md5,
  sha1,
  sha256
};
