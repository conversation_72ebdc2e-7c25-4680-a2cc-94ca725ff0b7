// database.js
import sqlite3 from 'sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const dbPath = path.join(__dirname, 'course.db');

let db = null;

export const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database', err.message);
        return reject(err);
      }
      console.log('Connected to the SQLite database.');

      // 创建 resources 表
      db.run(`CREATE TABLE IF NOT EXISTS resources (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        imageUrl TEXT,
        price REAL NOT NULL,
        baiduYunUrl TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`, (err) => {
        if (err) {
          console.error('Error creating resources table', err.message);
          return reject(err);
        }
        console.log('Resources table created or already exists.');
        resolve();
      });
    });
  });
};

export const getDb = () => {
  if (!db) {
    throw new Error('Database not initialized.');
  }
  return db;
};

export const closeDatabase = () => {
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('Error closing database', err.message);
      } else {
        console.log('Database connection closed.');
      }
    });
  }
};

// 确保在程序退出时关闭数据库
process.on('exit', closeDatabase);
process.on('SIGINT', closeDatabase); // 处理 Ctrl+C