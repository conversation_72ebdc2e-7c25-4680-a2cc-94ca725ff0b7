// index.js
import { Hono } from 'hono';
import { cors } from 'hono/cors'; // 引入 cors 中间件
import { serve } from '@hono/node-server';

import { initializeDatabase, getDb } from './database.js';

// 管理员登录信息
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123root';

const app = new Hono();

// 使用 CORS 中间件
app.use(cors());

// 数据库初始化
app.use(async (c, next) => {
  await initializeDatabase();
  await initializeSampleData();
  await next();
});

// 初始化示例数据
const initializeSampleData = async () => {
  const db = getDb();

  // 检查是否已有数据
  const count = await new Promise((resolve, reject) => {
    db.get('SELECT COUNT(*) as count FROM resources', (err, row) => {
      if (err) {
        console.error('Error checking data count', err.message);
        reject(err);
      }
      resolve(row.count);
    });
  });

  // 如果没有数据，插入示例数据
  if (count === 0) {
    console.log('Initializing sample data...');

    const sampleCourses = [
      {
        title: 'React 入门到精通',
        description: '从零开始学习 React，包括组件、状态管理、路由等核心概念。适合初学者和有一定基础的开发者。',
        imageUrl: 'https://picsum.photos/400/300?random=1',
        price: 299.99,
        baiduYunUrl: 'https://pan.baidu.com/s/example1'
      },
      {
        title: 'Vue.js 全栈开发',
        description: '学习 Vue.js 框架的完整开发流程，包括前端组件开发和后端 API 集成。',
        imageUrl: 'https://picsum.photos/400/300?random=2',
        price: 399.99,
        baiduYunUrl: 'https://pan.baidu.com/s/example2'
      },
      {
        title: 'Node.js 后端开发',
        description: '掌握 Node.js 后端开发技术，包括 Express 框架、数据库操作和 API 设计。',
        imageUrl: 'https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Node',
        price: 499.99,
        baiduYunUrl: 'https://pan.baidu.com/s/example3'
      }
    ];

    for (const course of sampleCourses) {
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO resources (title, description, imageUrl, price, baiduYunUrl) VALUES (?, ?, ?, ?, ?)',
          [course.title, course.description, course.imageUrl, course.price, course.baiduYunUrl],
          function (err) {
            if (err) {
              console.error('Error inserting sample data', err.message);
              reject(err);
            }
            console.log(`Inserted course: ${course.title} with ID: ${this.lastID}`);
            resolve();
          }
        );
      });
    }

    console.log('Sample data initialization completed.');
  }
};

// --- API 路由 ---

// 1. 用户认证 (登录)
app.post('/api/login', async (c) => {
  const { username, password } = await c.req.json();

  if (username === ADMIN_USERNAME && password === ADMIN_PASSWORD) {
    // 在实际应用中，这里会返回一个 token
    // 为了简单起见，我们直接返回一个成功的消息
    return c.json({ success: true, message: '登录成功，欢迎管理员！' });
  } else {
    return c.json({ success: false, message: '用户名或密码错误' }, 401);
  }
});

// 2. 课程/资料管理

// 获取所有课程/资料列表
app.get('/api/resources', async (c) => {
  const db = getDb();
  const resources = await new Promise((resolve, reject) => {
    db.all('SELECT * FROM resources', (err, rows) => {
      if (err) {
        console.error('Error fetching resources', err.message);
        reject(err);
      }
      resolve(rows);
    });
  });
  return c.json(resources);
});

// 获取单个课程/资料详情
app.get('/api/resources/:id', async (c) => {
  const id = c.req.param('id');
  const db = getDb();
  const resource = await new Promise((resolve, reject) => {
    db.get('SELECT * FROM resources WHERE id = ?', [id], (err, row) => {
      if (err) {
        console.error('Error fetching resource by id', err.message);
        reject(err);
      }
      resolve(row);
    });
  });

  if (!resource) {
    return c.json({ message: '未找到该资源' }, 404);
  }
  return c.json(resource);
});

// 发布新课程/资料
app.post('/api/resources', async (c) => {
  const db = getDb();
  const { title, description, imageUrl, price, baiduYunUrl } = await c.req.json();

  if (!title || price === undefined) {
    return c.json({ message: '标题和价格是必填项' }, 400);
  }

  try {
    const result = await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO resources (title, description, imageUrl, price, baiduYunUrl) VALUES (?, ?, ?, ?, ?)',
        [title, description, imageUrl, price, baiduYunUrl],
        function (err) { // 使用 function 关键字以访问 this.lastID
          if (err) {
            console.error('Error inserting resource', err.message);
            reject(err);
          }
          resolve({ id: this.lastID });
        }
      );
    });
    return c.json({ message: '资源发布成功', id: result.id }, 201);
  } catch (error) {
    return c.json({ message: '资源发布失败', error: error.message }, 500);
  }
});

// 更新课程/资料
app.put('/api/resources/:id', async (c) => {
  const id = c.req.param('id');
  const db = getDb();
  const { title, description, imageUrl, price, baiduYunUrl } = await c.req.json();

  if (!title || price === undefined) {
    return c.json({ message: '标题和价格是必填项' }, 400);
  }

  try {
    const result = await new Promise((resolve, reject) => {
      db.run(
        'UPDATE resources SET title = ?, description = ?, imageUrl = ?, price = ?, baiduYunUrl = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
        [title, description, imageUrl, price, baiduYunUrl, id],
        function (err) {
          if (err) {
            console.error('Error updating resource', err.message);
            reject(err);
          }
          resolve(this.changes); // this.changes 包含受影响的行数
        }
      );
    });

    if (result === 0) {
      return c.json({ message: '未找到该资源进行更新' }, 404);
    }

    return c.json({ message: '资源更新成功' });
  } catch (error) {
    return c.json({ message: '资源更新失败', error: error.message }, 500);
  }
});

// 删除课程/资料
app.delete('/api/resources/:id', async (c) => {
  const id = c.req.param('id');
  const db = getDb();

  try {
    const result = await new Promise((resolve, reject) => {
      db.run('DELETE FROM resources WHERE id = ?', [id], function (err) {
        if (err) {
          console.error('Error deleting resource', err.message);
          reject(err);
        }
        resolve(this.changes);
      });
    });

    if (result === 0) {
      return c.json({ message: '未找到该资源进行删除' }, 404);
    }

    return c.json({ message: '资源删除成功' });
  } catch (error) {
    return c.json({ message: '资源删除失败', error: error.message }, 500);
  }
});

// 3. 订单管理

// 创建订单
app.post('/api/orders', async (c) => {
  const db = getDb();
  const { resourceId, phone, paymentMethod = 'manual' } = await c.req.json();

  if (!resourceId || !phone) {
    return c.json({ message: '资源ID和手机号是必填项' }, 400);
  }

  // 检查资源是否存在
  const resource = await new Promise((resolve, reject) => {
    db.get('SELECT * FROM resources WHERE id = ?', [resourceId], (err, row) => {
      if (err) {
        console.error('Error fetching resource', err.message);
        reject(err);
      }
      resolve(row);
    });
  });

  if (!resource) {
    return c.json({ message: '资源不存在' }, 404);
  }

  try {
    // 创建订单表（如果不存在）
    await new Promise((resolve, reject) => {
      db.run(`CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resourceId INTEGER NOT NULL,
        resourceTitle TEXT NOT NULL,
        phone TEXT NOT NULL,
        amount REAL NOT NULL,
        paymentMethod TEXT DEFAULT 'manual',
        status TEXT DEFAULT 'pending',
        paymentProof TEXT,
        notes TEXT,
        reviewNotes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (resourceId) REFERENCES resources (id)
      )`, (err) => {
        if (err) {
          console.error('Error creating orders table', err.message);
          reject(err);
        } else {
          resolve();
        }
      });
    });

    // 添加 reviewNotes 列（如果不存在）
    await new Promise((resolve, reject) => {
      db.run(`ALTER TABLE orders ADD COLUMN reviewNotes TEXT`, (err) => {
        // 忽略列已存在的错误
        if (err && !err.message.includes('duplicate column name')) {
          console.error('Error adding reviewNotes column', err.message);
          reject(err);
        } else {
          resolve();
        }
      });
    });

    // 插入订单
    const result = await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO orders (resourceId, resourceTitle, phone, amount, paymentMethod) VALUES (?, ?, ?, ?, ?)',
        [resourceId, resource.title, phone, resource.price, paymentMethod],
        function (err) {
          if (err) {
            console.error('Error creating order', err.message);
            reject(err);
          }
          resolve({ id: this.lastID });
        }
      );
    });

    return c.json({
      message: '订单创建成功',
      orderId: result.id,
      amount: resource.price,
      resourceTitle: resource.title
    }, 201);
  } catch (error) {
    return c.json({ message: '订单创建失败', error: error.message }, 500);
  }
});

// 上传支付凭证
app.post('/api/orders/:orderId/payment-proof', async (c) => {
  const orderId = c.req.param('orderId');
  const db = getDb();
  const { paymentProof, notes, paymentTime } = await c.req.json();

  if (!paymentProof) {
    return c.json({ message: '支付凭证是必填项' }, 400);
  }

  try {
    // 检查订单是否存在
    const order = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM orders WHERE id = ?', [orderId], (err, row) => {
        if (err) {
          console.error('Error fetching order', err.message);
          reject(err);
        }
        resolve(row);
      });
    });

    if (!order) {
      return c.json({ message: '订单不存在' }, 404);
    }

    // 更新订单支付凭证
    const result = await new Promise((resolve, reject) => {
      db.run(
        'UPDATE orders SET paymentProof = ?, notes = ?, status = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
        [paymentProof, notes || '', 'submitted', orderId],
        function (err) {
          if (err) {
            console.error('Error updating order payment proof', err.message);
            reject(err);
          }
          resolve(this.changes);
        }
      );
    });

    if (result === 0) {
      return c.json({ message: '订单更新失败' }, 404);
    }

    return c.json({
      message: '支付凭证上传成功，等待审核',
      orderId: orderId,
      status: 'submitted'
    });
  } catch (error) {
    return c.json({ message: '支付凭证上传失败', error: error.message }, 500);
  }
});

// 获取订单详情
app.get('/api/orders/:orderId', async (c) => {
  const orderId = c.req.param('orderId');
  const db = getDb();

  try {
    const order = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM orders WHERE id = ?', [orderId], (err, row) => {
        if (err) {
          console.error('Error fetching order', err.message);
          reject(err);
        }
        resolve(row);
      });
    });

    if (!order) {
      return c.json({ message: '订单不存在' }, 404);
    }

    return c.json(order);
  } catch (error) {
    return c.json({ message: '获取订单失败', error: error.message }, 500);
  }
});

// 管理员审核支付凭证
app.post('/api/orders/:orderId/review', async (c) => {
  const orderId = c.req.param('orderId');
  const db = getDb();
  const { action, reviewNotes } = await c.req.json(); // action: 'approve' 或 'reject'

  if (!action || !['approve', 'reject'].includes(action)) {
    return c.json({ message: '审核操作无效，必须是 approve 或 reject' }, 400);
  }

  try {
    const newStatus = action === 'approve' ? 'approved' : 'rejected';

    const result = await new Promise((resolve, reject) => {
      db.run(
        'UPDATE orders SET status = ?, reviewNotes = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
        [newStatus, reviewNotes || '', orderId],
        function (err) {
          if (err) {
            console.error('Error reviewing order', err.message);
            reject(err);
          }
          resolve(this.changes);
        }
      );
    });

    if (result === 0) {
      return c.json({ message: '订单不存在' }, 404);
    }

    return c.json({
      message: `订单${action === 'approve' ? '审核通过' : '审核拒绝'}`,
      orderId: orderId,
      status: newStatus
    });
  } catch (error) {
    return c.json({ message: '审核失败', error: error.message }, 500);
  }
});

// 获取所有订单（管理员用）
app.get('/api/orders', async (c) => {
  const db = getDb();
  const { status, page = 1, limit = 10 } = c.req.query();

  try {
    let query = 'SELECT * FROM orders';
    let params = [];

    if (status) {
      query += ' WHERE status = ?';
      params.push(status);
    }

    query += ' ORDER BY createdAt DESC';

    if (limit && limit !== 'all') {
      const offset = (parseInt(page) - 1) * parseInt(limit);
      query += ' LIMIT ? OFFSET ?';
      params.push(parseInt(limit), offset);
    }

    const orders = await new Promise((resolve, reject) => {
      db.all(query, params, (err, rows) => {
        if (err) {
          console.error('Error fetching orders', err.message);
          reject(err);
        }
        resolve(rows);
      });
    });

    return c.json(orders);
  } catch (error) {
    return c.json({ message: '获取订单列表失败', error: error.message }, 500);
  }
});

// 4. 下载支付 (模拟)

// 请求下载链接/支付信息 (前端先调用此接口获取价格等信息)
app.get('/api/download/:resourceId', async (c) => {
  const resourceId = c.req.param('resourceId');
  const db = getDb();

  const resource = await new Promise((resolve, reject) => {
    db.get('SELECT id, title, price, baiduYunUrl FROM resources WHERE id = ?', [resourceId], (err, row) => {
      if (err) {
        console.error('Error fetching resource for download', err.message);
        reject(err);
      }
      resolve(row);
    });
  });

  if (!resource) {
    return c.json({ message: '未找到该资源' }, 404);
  }

  // 在实际支付流程中，这里会生成一个支付链接或返回支付所需信息
  // 这里我们只返回资源的基本信息和价格，模拟前端触发支付
  return c.json({
    resourceId: resource.id,
    title: resource.title,
    price: resource.price,
    message: '请进行支付以获取下载链接。',
    // 模拟支付成功后会返回 baiduYunUrl
    // 真实情况是支付完成后，由支付平台回调通知服务器，服务器再授予访问权
  });
});

// 验证支付并返回下载链接 (基于订单)
app.post('/api/verify-payment/:orderId', async (c) => {
  const orderId = c.req.param('orderId');
  const db = getDb();

  try {
    // 获取订单信息
    const order = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM orders WHERE id = ?', [orderId], (err, row) => {
        if (err) {
          console.error('Error fetching order for payment verification', err.message);
          reject(err);
        }
        resolve(row);
      });
    });

    if (!order) {
      return c.json({ message: '订单不存在' }, 404);
    }

    // 检查订单状态
    if (order.status !== 'approved') {
      return c.json({
        success: false,
        message: '订单尚未审核通过，无法获取下载链接',
        status: order.status
      }, 400);
    }

    // 获取资源信息
    const resource = await new Promise((resolve, reject) => {
      db.get('SELECT id, baiduYunUrl FROM resources WHERE id = ?', [order.resourceId], (err, row) => {
        if (err) {
          console.error('Error fetching resource for download', err.message);
          reject(err);
        }
        resolve(row);
      });
    });

    if (!resource || !resource.baiduYunUrl) {
      return c.json({ success: false, message: '资源不存在或下载链接不可用' }, 404);
    }

    // 返回下载链接
    return c.json({
      success: true,
      message: '支付验证成功，为您提供下载链接！',
      downloadUrl: resource.baiduYunUrl,
      orderId: order.id,
      resourceId: resource.id,
      resourceTitle: order.resourceTitle
    });
  } catch (error) {
    return c.json({ success: false, message: '支付验证失败', error: error.message }, 500);
  }
});


// 基础健康检查接口
app.get('/', (c) => {
  return c.text('Backend is running!');
});

// 启动服务器
const port = process.env.PORT || 3000;

serve({
  fetch: app.fetch,
  port: port,
}, (info) => {
  console.log(`Server is running on http://localhost:${info.port}`);
});

// 导出 app 以便运行
export default app;