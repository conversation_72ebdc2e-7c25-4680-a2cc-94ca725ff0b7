"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var language_exports = {};
__export(language_exports, {
  detectFromCookie: () => import_language.detectFromCookie,
  detectFromHeader: () => import_language.detectFromHeader,
  detectFromPath: () => import_language.detectFromPath,
  detectFromQuery: () => import_language.detectFromQuery,
  languageDetector: () => import_language.languageDetector
});
module.exports = __toCommonJS(language_exports);
var import_language = require("./language");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  detectFromCookie,
  detectFromHeader,
  detectFromPath,
  detectFromQuery,
  languageDetector
});
