# 在线课程商城 API 文档

## 📋 概述

本文档描述了在线课程商城后端API的所有接口，包括用户认证、课程管理、订单管理等功能。

### 🔗 基础信息
- **服务器地址**: `http://localhost:3000`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **API版本**: v1.0

---

## 🔐 用户认证

### 管理员登录
```http
POST /api/login
```

**请求体**:
```json
{
  "username": "admin",
  "password": "admin123root"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "登录成功，欢迎管理员！"
}
```

**失败响应** (401):
```json
{
  "success": false,
  "message": "用户名或密码错误"
}
```

---

## 📚 课程资源管理

### 1. 获取所有课程
```http
GET /api/resources
```

**成功响应** (200):
```json
[
  {
    "id": 1,
    "title": "React 入门到精通",
    "description": "从零开始学习 React，包括组件、状态管理、路由等核心概念",
    "imageUrl": "https://example.com/react.jpg",
    "price": 299.99,
    "baiduYunUrl": "https://pan.baidu.com/s/example1",
    "createdAt": "2025-06-28 15:22:36",
    "updatedAt": "2025-06-28 15:22:36"
  }
]
```

### 2. 获取单个课程
```http
GET /api/resources/{id}
```

**成功响应** (200):
```json
{
  "id": 1,
  "title": "React 入门到精通",
  "description": "从零开始学习 React，包括组件、状态管理、路由等核心概念",
  "imageUrl": "https://example.com/react.jpg",
  "price": 299.99,
  "baiduYunUrl": "https://pan.baidu.com/s/example1",
  "createdAt": "2025-06-28 15:22:36",
  "updatedAt": "2025-06-28 15:22:36"
}
```

**失败响应** (404):
```json
{
  "message": "未找到该资源"
}
```

### 3. 创建课程
```http
POST /api/resources
```

**请求体**:
```json
{
  "title": "课程标题",           // 必填
  "description": "课程描述",     // 可选
  "imageUrl": "图片URL",        // 可选
  "price": 299.99,             // 必填
  "baiduYunUrl": "下载链接"     // 可选
}
```

**成功响应** (201):
```json
{
  "message": "资源发布成功",
  "id": 4
}
```

**失败响应** (400):
```json
{
  "message": "标题和价格是必填项"
}
```

### 4. 更新课程
```http
PUT /api/resources/{id}
```

**请求体**:
```json
{
  "title": "更新的课程标题",
  "description": "更新的课程描述",
  "imageUrl": "更新的图片URL",
  "price": 399.99,
  "baiduYunUrl": "更新的下载链接"
}
```

**成功响应** (200):
```json
{
  "message": "资源更新成功"
}
```

**失败响应** (404):
```json
{
  "message": "未找到该资源"
}
```

### 5. 删除课程
```http
DELETE /api/resources/{id}
```

**成功响应** (200):
```json
{
  "message": "资源删除成功"
}
```

**失败响应** (404):
```json
{
  "message": "未找到该资源"
}
```

---

## 🛒 订单管理

### 1. 创建订单
```http
POST /api/orders
```

**请求体**:
```json
{
  "resourceId": 1,           // 必填，课程资源ID
  "phone": "13800138000"     // 必填，用户手机号
}
```

**成功响应** (200):
```json
{
  "message": "订单创建成功",
  "orderId": 1,
  "amount": 299.99,
  "resourceTitle": "React 入门到精通"
}
```

**失败响应** (400):
```json
{
  "message": "资源不存在"
}
```

### 2. 上传支付凭证
```http
POST /api/orders/{orderId}/payment-proof
```

**请求体**:
```json
{
  "paymentProof": "payment_screenshot.jpg",  // 必填，支付凭证文件名
  "notes": "支付宝转账凭证"                    // 可选，备注信息
}
```

**成功响应** (200):
```json
{
  "message": "支付凭证上传成功，等待审核",
  "orderId": 1,
  "status": "submitted"
}
```

**失败响应** (400):
```json
{
  "message": "支付凭证是必填项"
}
```

### 3. 获取订单详情
```http
GET /api/orders/{orderId}
```

**成功响应** (200):
```json
{
  "id": 1,
  "resourceId": 1,
  "resourceTitle": "React 入门到精通",
  "phone": "13800138000",
  "amount": 299.99,
  "paymentMethod": "manual",
  "status": "submitted",
  "paymentProof": "payment_screenshot.jpg",
  "notes": "支付宝转账凭证",
  "reviewNotes": "审核备注",
  "createdAt": "2025-06-28 15:22:51",
  "updatedAt": "2025-06-28 15:23:06"
}
```

### 4. 获取所有订单（管理员）
```http
GET /api/orders
```

**成功响应** (200):
```json
[
  {
    "id": 1,
    "resourceId": 1,
    "resourceTitle": "React 入门到精通",
    "phone": "13800138000",
    "amount": 299.99,
    "paymentMethod": "manual",
    "status": "approved",
    "paymentProof": "payment_screenshot.jpg",
    "notes": "支付宝转账凭证",
    "reviewNotes": "审核通过",
    "createdAt": "2025-06-28 15:22:51",
    "updatedAt": "2025-06-28 15:23:06"
  }
]
```

### 5. 管理员审核订单
```http
POST /api/orders/{orderId}/review
```

**请求体**:
```json
{
  "action": "approve",                    // 必填，"approve" 或 "reject"
  "reviewNotes": "支付凭证有效，审核通过"    // 可选，审核备注
}
```

**成功响应** (200):
```json
{
  "message": "订单审核通过",
  "orderId": 1,
  "status": "approved"
}
```

### 6. 验证支付并获取下载链接
```http
POST /api/verify-payment/{orderId}
```

**请求体**:
```json
{}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "支付验证成功，为您提供下载链接！",
  "downloadUrl": "https://pan.baidu.com/s/example1",
  "orderId": 1,
  "resourceId": 1,
  "resourceTitle": "React 入门到精通"
}
```

**失败响应** (400):
```json
{
  "success": false,
  "message": "订单尚未审核通过，无法获取下载链接",
  "status": "submitted"
}
```

---

## 📊 数据模型

### 课程资源 (Resource)
```json
{
  "id": "整数，主键",
  "title": "字符串，课程标题",
  "description": "字符串，课程描述",
  "imageUrl": "字符串，课程封面图片URL",
  "price": "浮点数，课程价格",
  "baiduYunUrl": "字符串，百度云下载链接",
  "createdAt": "日期时间，创建时间",
  "updatedAt": "日期时间，更新时间"
}
```

### 订单 (Order)
```json
{
  "id": "整数，主键",
  "resourceId": "整数，关联的课程ID",
  "resourceTitle": "字符串，课程标题",
  "phone": "字符串，用户手机号",
  "amount": "浮点数，订单金额",
  "paymentMethod": "字符串，支付方式，默认'manual'",
  "status": "字符串，订单状态",
  "paymentProof": "字符串，支付凭证文件名",
  "notes": "字符串，用户备注",
  "reviewNotes": "字符串，管理员审核备注",
  "createdAt": "日期时间，创建时间",
  "updatedAt": "日期时间，更新时间"
}
```

### 订单状态说明
| 状态 | 说明 |
|------|------|
| `pending` | 待支付，订单已创建，等待上传支付凭证 |
| `submitted` | 已提交，支付凭证已上传，等待管理员审核 |
| `approved` | 已通过，管理员审核通过，可以下载资源 |
| `rejected` | 已拒绝，管理员审核拒绝，需要重新上传凭证 |

---

## 🔧 前端集成示例

### JavaScript/Fetch 示例

#### 创建订单
```javascript
const createOrder = async (resourceId, phone) => {
  try {
    const response = await fetch('http://localhost:3000/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        resourceId: resourceId,
        phone: phone
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('创建订单失败:', error);
    throw error;
  }
};
```

#### 获取课程列表
```javascript
const getCourses = async () => {
  try {
    const response = await fetch('http://localhost:3000/api/resources');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取课程列表失败:', error);
    throw error;
  }
};
```

#### 管理员登录
```javascript
const adminLogin = async (username, password) => {
  try {
    const response = await fetch('http://localhost:3000/api/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: username,
        password: password
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 登录成功，可以保存登录状态
      localStorage.setItem('isLoggedIn', 'true');
      return true;
    } else {
      console.log('登录失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('登录请求失败:', error);
    return false;
  }
};
```

---

## ⚠️ 注意事项

1. **错误处理**: 所有接口都需要处理HTTP状态码和错误响应
2. **数据验证**: 前端需要验证必填字段和数据格式
3. **权限控制**: 管理员功能需要先调用登录接口验证身份
4. **实时更新**: 建议定期轮询订单状态或使用WebSocket实现实时更新
5. **文件上传**: 支付凭证目前只传文件名，实际项目中需要实现文件上传功能
6. **跨域处理**: 如果前后端不在同一域名下，需要配置CORS
7. **安全性**: 生产环境中需要添加身份验证、输入验证等安全措施

---

## 🚀 快速开始

1. 启动后端服务器：
   ```bash
   npm run dev
   ```

2. 服务器运行在 `http://localhost:3000`

3. 测试API连接：
   ```bash
   curl http://localhost:3000/api/resources
   ```

4. 管理员登录测试：
   ```bash
   curl -X POST http://localhost:3000/api/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123root"}'
   ```

---

## 📞 技术支持

如有问题，请检查：
1. 服务器是否正常运行
2. 请求格式是否正确
3. 必填参数是否完整
4. 网络连接是否正常

---

*最后更新时间: 2025-06-28*
